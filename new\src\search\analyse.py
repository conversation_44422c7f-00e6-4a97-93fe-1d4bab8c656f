import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from llm_search_host import LLM_search

logger = logging.getLogger(__name__)


class AnalyseInterface:

    def __init__(self, base_dir: str = "new/test"):

        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"AnalyseInterface initialized with base_dir: {self.base_dir}")
    
    def analyse(self, task: str, description: Optional[str] = None, 
                top_n: int = 10, **kwargs) -> str:

        logger.info(f"Starting analysis for task: {task}")
        

        task_dir = self._create_task_directory(task)
        
        papers = self._search_literature(task, description, top_n, **kwargs)
        
        self._save_papers_to_directory(papers, task_dir)
        
        logger.info(f"Analysis completed. Papers saved to: {task_dir}")
        return str(task_dir)
    
    def _create_task_directory(self, task: str) -> Path:

        safe_task_name = "".join(c for c in task if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_task_name = safe_task_name.replace(' ', '_')
        
        task_dir = self.base_dir / safe_task_name
        task_dir.mkdir(parents=True, exist_ok=True)
        
        return task_dir
    
    def _search_literature(self, task: str, description: Optional[str], 
                          top_n: int, **kwargs) -> List[Dict[str, Any]]:

        logger.info(f"Searching literature for task: {task}")
        
        # 返回survey类
        papers = []
        
        for i in range(min(top_n, 10)):
            paper = {
                "title": f"Research Paper {i+1}: {task}",
                "abstract": f"This paper discusses {task} and related methodologies. "
                           f"Abstract content for paper {i+1}...",
                "authors": [f"Author{i+1}A", f"Author{i+1}B"],
                "year": 2024 - i % 5,
                "venue": f"Conference/Journal {i+1}",
                "url": f"https://example.com/paper_{i+1}",
                "txt": f"Full text content of paper {i+1} about {task}. "
                       f"This includes detailed methodology, experiments, and conclusions...",
                "keywords": [task.lower(), "research", "methodology"],
                "doi": f"10.1000/paper{i+1}",
                "citations": 50 - i * 5
            }
            papers.append(paper)
        
        logger.info(f"Found {len(papers)} papers for task: {task}")
        return papers
    
    def _save_papers_to_directory(self, papers: List[Dict[str, Any]], 
                                 task_dir: Path) -> None:

        try:
            for i, paper in enumerate(papers):
                paper_filename = f"paper_{i+1:03d}.json"
                paper_path = task_dir / paper_filename
                
                with open(paper_path, 'w', encoding='utf-8') as f:
                    json.dump(paper, f, ensure_ascii=False, indent=2)
            
            summary_data = {
                "task": task_dir.name,
                "total_papers": len(papers),
                "paper_files": [f"paper_{i+1:03d}.json" for i in range(len(papers))],
                "created_at": str(Path().cwd())
            }
            
            summary_path = task_dir / "summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Saved {len(papers)} papers to {task_dir}")
            
        except Exception as e:
            logger.error(f"Error saving papers to directory: {e}")
            raise


def analyse(task: str, description: Optional[str] = None, 
           top_n: int = 10, **kwargs) -> str:

    analyser = AnalyseInterface()
    return analyser.analyse(task, description, top_n, **kwargs)


if __name__ == "__main__"
    # 测试代码
    test_task = "machine learning optimization"
    result_dir = analyse(test_task, "Research on ML optimization techniques", top_n=5)
    print(f"Analysis completed. Results saved to: {result_dir}")
