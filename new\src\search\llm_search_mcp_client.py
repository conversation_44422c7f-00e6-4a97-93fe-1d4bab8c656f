import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from mcp import ClientSession
from mcp.client.stdio import stdio_client
from mcp.types import CallToolRequest, TextContent

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MCPClient:

    def __init__(self, server_config: Dict[str, Any]):
        self.server_config = server_config
        self.session = None
        self.read_stream = None
        self.write_stream = None
        self._connected = False

    async def connect(self):
        """连接到MCP服务器"""
        try:
            if self._connected:
                logger.warning("Already connected to MCP server")
                return

            logger.info("Connecting to MCP Server...")

            self.read_stream, self.write_stream = await stdio_client(
                self.server_config["command"],
                self.server_config.get("args", []),
                env=self.server_config.get("env", None)
            ).__aenter__()
            self.session = await ClientSession(self.read_stream, self.write_stream).__aenter__()

            await self.session.initialize()
            self._connected = True

            logger.info("Successfully connected to MCP server")

        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            self._connected = False
            raise

    async def disconnect(self):
        try:
            if not self._connected:
                return

            if self.session:
                await self.session.__aexit__(None, None, None)
            if self.read_stream and self.write_stream:
                await stdio_client(
                    self.server_config["command"],
                    self.server_config.get("args", []),
                    env=self.server_config.get("env", None)
                ).__aexit__(None, None, None)

            self._connected = False
            logger.info("Disconnected from MCP server")
        except Exception as e:
            logger.error(f"Error disconnecting from MCP server: {e}")

    async def list_tools(self) -> List[Dict[str, Any]]:
        """列出可用的工具"""
        try:
            if not self._connected or not self.session:
                raise RuntimeError("Not connected to MCP server")

            tools = await self.session.list_tools()
            logger.debug(f"Available tools: {[tool.name for tool in tools.tools]}")

            return [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema
                }
                for tool in tools.tools
            ]

        except Exception as e:
            logger.error(f"Error listing tools: {e}")
            raise

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        try:
            if not self._connected or not self.session:
                raise RuntimeError("Not connected to MCP server")

            logger.debug(f"Calling tool: {tool_name} with arguments: {arguments}")

            result = await self.session.call_tool(
                CallToolRequest(
                    name=tool_name,
                    arguments=arguments
                )
            )

            if result.content and len(result.content) > 0:
                content = result.content[0]
                if isinstance(content, TextContent):
                    return json.loads(content.text)

            raise ValueError(f"No valid response received from tool {tool_name}")

        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            raise

    async def list_resources(self) -> List[Dict[str, Any]]:
        try:
            if not self._connected or not self.session:
                raise RuntimeError("Not connected to MCP server")

            resources = await self.session.list_resources()
            logger.debug(f"Available resources: {[resource.uri for resource in resources.resources]}")

            return [
                {
                    "uri": resource.uri,
                    "name": resource.name,
                    "description": resource.description,
                    "mimeType": resource.mimeType
                }
                for resource in resources.resources
            ]

        except Exception as e:
            logger.error(f"Error listing resources: {e}")
            raise

    async def read_resource(self, uri: str) -> str:
        try:
            if not self._connected or not self.session:
                raise RuntimeError("Not connected to MCP server")

            result = await self.session.read_resource(uri)
            return result.contents[0].text if result.contents else ""

        except Exception as e:
            logger.error(f"Error reading resource {uri}: {e}")
            raise

    @property
    def is_connected(self) -> bool:
        return self._connected

async def create_mcp_client(server_config: Dict[str, Any]) -> MCPClient:

    try:
        client = MCPClient(server_config)
        await client.connect()
        return client

    except Exception as e:
        logger.error(f"Failed to create MCP client: {e}")
        raise

async def create_mcp_client_from_config(config_path: str = "config/llm_search_mcp_config.json",
                                       server_name: str = "llm_search_server") -> MCPClient:
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        server_config = config["servers"][server_name]
        return await create_mcp_client(server_config)

    except Exception as e:
        logger.error(f"Failed to create MCP client from config: {e}")
        raise

async def example_usage():
    """示例用法"""
    client = None
    try:

        client = await create_mcp_client_from_config()

        tools = await client.list_tools()
        print("Available tools:", [tool["name"] for tool in tools])

        result = await client.call_tool(
            "generate_search_queries",
            {
                "topic": "machine learning optimization",
                "description": "Research on optimization techniques in machine learning"
            }
        )

        print("Tool result:")
        print(json.dumps(result, ensure_ascii=False, indent=2))

    except Exception as e:
        logger.error(f"Example usage failed: {e}")
    finally:
        if client:
            await client.disconnect()

if __name__ == "__main__":
    asyncio.run(example_usage())
